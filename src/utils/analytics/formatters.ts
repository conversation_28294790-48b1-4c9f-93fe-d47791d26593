import type {AppUser, DeviceStateType, HealthStats} from '@types';
import {getSumMileage, getSumSteps} from '../health';

export const formatHealthSyncParams = (updates?: HealthStats[]) => {
  const steps = getSumSteps(updates ?? []);
  const miles = getSumMileage(updates ?? []);

  return {
    ...(steps && {steps}),
    ...(miles && {miles}),
  };
};

export const formatUserProperties = (
  appUser: AppUser,
  device: DeviceStateType,
) => ({
  /* eslint-disable @typescript-eslint/naming-convention, camelcase -- analytics */
  email: appUser.email,
  user_type: appUser.type,
  phone_number: appUser.phoneNumber ?? 'none',
  time_zone: appUser.timeZone ?? Intl.DateTimeFormat().resolvedOptions().timeZone,
  tracking_device: appUser.trackingDeviceType ?? 'none',
  tracking_app: appUser.trackingApp ?? 'none',
  os: device.osName ?? 'none',
  os_version: device.osVersion ?? 'none',
  phone_brand: device.brand ?? 'none',
  phone_model: device.modelName ?? 'none',
  /* eslint-enable @typescript-eslint/naming-convention, camelcase -- analytics */
});
