import {Box, Text} from '@base-components';
import {useAppUserSafe, useTrackingDeviceType} from '@contexts';
import {getTrackingDeviceTypeDisplayName, timestampToDate} from '@types';
import {formatDisplayDayMonthYear} from '@utils';

export const LastIntegrationSwitchInfo: React.FC = () => {
  const appUser = useAppUserSafe();
  const currentDeviceType = useTrackingDeviceType();
  const lastSwitch = appUser.lastHealthIntegrationSwitch;

  if (!lastSwitch) return null;

  const switchDate = timestampToDate(lastSwitch.switchDate);
  const formattedDate = formatDisplayDayMonthYear(switchDate);

  return (
    <Box pt={1}>
      <Text style={{fontStyle: 'italic'}} variant='bodySmall'>
        Last integration switch: {formattedDate}
        {lastSwitch.previousDeviceType && currentDeviceType && (
          <>
            {' '}
            (from {getTrackingDeviceTypeDisplayName(lastSwitch.previousDeviceType)} to{' '}
            {getTrackingDeviceTypeDisplayName(currentDeviceType)})
          </>
        )}
      </Text>
    </Box>
  );
};
