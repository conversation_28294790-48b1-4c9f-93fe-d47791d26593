import {type ReactNode, useEffect} from 'react';
import {isProd} from '@constants';
import {formatUserProperties, getDeviceState} from '@utils';
import type {AnalyticsService} from '../analytics/analyticsService';
import {useAppUser} from '../authContext';
import {firebaseAnalyticsService} from '../analytics/firebaseAnalytics';
import {AnalyticsContext, defaultService} from '../analytics';

export const AnalyticsProvider: React.FC<{children: ReactNode}> = ({children}) => {
  // This can be changed to determine provider from Expo config extra.analyticsProvider
  const service: AnalyticsService = isProd ? firebaseAnalyticsService() : defaultService;
  const appUser = useAppUser();

  useEffect(() => {
    if (!appUser) return;

    const device = getDeviceState();
    void service.setUserId(appUser.id);
    void service.setUserProperties(formatUserProperties(appUser, device));
  }, [appUser, service]);

  return <AnalyticsContext.Provider value={service}>{children}</AnalyticsContext.Provider>;
};
