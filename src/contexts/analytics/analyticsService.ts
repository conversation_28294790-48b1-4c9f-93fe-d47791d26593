export type AnalyticsService = {
  /** Log a custom event */
  logEvent: (name: string, params?: Record<string, unknown>) => Promise<void>;

  /** Record current screen name */
  setCurrentScreen: (screenName: string) => Promise<void>;

  /** Set the Firebase Analytics user ID */
  setUserId: (id: string) => Promise<void>;

  /** Set multiple user properties at once */
  setUserProperties: (props: Record<string, string>) => Promise<void>;
};
