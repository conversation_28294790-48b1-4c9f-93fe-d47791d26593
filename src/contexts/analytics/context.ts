// eslint-disable-next-line @typescript-eslint/no-restricted-imports -- for analytics context
import {createContext, useContext} from 'react';
import type {AnalyticsService} from './analyticsService';
import {consoleAnalytics} from './consoleAnalytics';

export const defaultService = consoleAnalytics();

// eslint-disable-next-line @typescript-eslint/naming-convention -- context naming convention
export const AnalyticsContext = createContext<AnalyticsService>(defaultService);

export const useAnalytics = () => useContext(AnalyticsContext);
