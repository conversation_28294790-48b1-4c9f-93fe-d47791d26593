import {analyticsConsoleLog} from '@utils';
import type {AnalyticsService} from './analyticsService';

export const consoleAnalytics = (): AnalyticsService => ({
  logEvent: async (name, params) => {
    analyticsConsoleLog(`[event] ${name}`, params);
  },

  setCurrentScreen: async screenName => {
    analyticsConsoleLog(`[screen] ${screenName}`);
  },

  setUserId: async id => {
    analyticsConsoleLog('[userId]', id);
  },

  setUserProperties: async props => {
    analyticsConsoleLog('[userProperties]', props);
  },
});
