name: PR Tests

# Cancel redundant runs when new commits are pushed
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - main
    # Skip CI for draft PRs and docs-only changes
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'
      - 'LICENSE'

jobs:
  test:
    name: Run Tests
    runs-on: macos-13 # ubuntu-latest-4-cores # must run with a runner with 8GB+ of RAM, ubuntu only has 7GB
    # Skip draft PRs
    if: github.event.pull_request.draft == false
    env:
      NODE_OPTIONS: '--max-old-space-size=8192'
    steps:
      - name: 🏗️ Checkout code
        uses: actions/checkout@v4
        with:
          # Only fetch the specific commit, not full history
          fetch-depth: 1

      - name: 🏗️ Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: |
            **/package-lock.json

      - name: 📦 Install dependencies (all modules)
        run: npm run ci:all

      - name: 🧪 Run tests
        run: npm run test:ci
        env:
          # Reduce Jest workers to prevent memory issues
          NODE_OPTIONS: '--max-old-space-size=8192'
