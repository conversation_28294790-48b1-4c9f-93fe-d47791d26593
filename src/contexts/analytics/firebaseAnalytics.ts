import analytics from '@react-native-firebase/analytics';
import type {AnalyticsService} from './analyticsService';

export const firebaseAnalyticsService = (): AnalyticsService => ({
  logEvent: async (name, params) => {
    await analytics().logEvent(name, params);
  },

  setCurrentScreen: async screenName => {
    await analytics().logScreenView({
      // eslint-disable-next-line @typescript-eslint/naming-convention, camelcase -- firebase
      screen_name: screenName,
      // eslint-disable-next-line @typescript-eslint/naming-convention, camelcase -- firebase
      screen_class: screenName,
    });
  },

  setUserId: async id => {
    await analytics().setUserId(id);
  },

  setUserProperties: async props => {
    // No batch write in RN Firebase — update props one by one
    await Promise.all(
      Object.entries(props).map(([key, value]) =>
        analytics().setUserProperty(key, value),
      ),
    );
  },
});
