import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import {Alert, Share} from 'react-native';
import {useCallback, useMemo} from 'react';
import * as Linking from 'expo-linking';
import {getFileExtension, LOGGER, uuid} from '@utils';
import {APP_NAME, SYSTEM_ICONS} from '@constants';
import {useAddSnack} from './snacks';

const saveImageToGallery = async (
  imageUrl: string | undefined,
  addSnack: (text: string) => void,
) => {
  if (!imageUrl) return;
  try {
    const {status} = await MediaLibrary.requestPermissionsAsync(true);

    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please allow access to save images');
      return;
    }

    // Get content type first to know what extension to use
    const headResponse = await fetch(imageUrl, {method: 'HEAD'});
    const contentType = headResponse.headers.get('content-type');
    const extension = getFileExtension(contentType);

    const fileUri = `${FileSystem.documentDirectory}${uuid()}.${extension}`;

    const {uri} = await FileSystem.downloadAsync(imageUrl, fileUri);

    const asset = await MediaLibrary.createAssetAsync(uri);
    await MediaLibrary.createAlbumAsync(APP_NAME, asset, false);

    LOGGER.debug('Image saved to gallery', {uri});
    addSnack('Image successfully saved');
  } catch (error) {
    LOGGER.error('Error saving image', error);
    addSnack('There was an issue saving the image');
  }
};

const useSaveImageToGallery = () => {
  const addSnack = useAddSnack();

  return useCallback(
    (imageUrl: string | undefined) => saveImageToGallery(imageUrl, addSnack),
    [addSnack],
  );
};

export const useImageContextActions = (imageUrl: string | undefined) => {
  const saveImage = useSaveImageToGallery();

  return (
    useMemo(
      () => [
        {
          title: 'Share image',
          onPress: async () => {
            if (!imageUrl) return;
            await Share.share({url: imageUrl});
          },
          icons: SYSTEM_ICONS.share,
        },
        {
          title: 'Save image',
          onPress: async () => {
            await saveImage(imageUrl);
          },
          icons: SYSTEM_ICONS.download,
        },
        {
          title: 'Open in browser',
          onPress: async () => {
            if (!imageUrl) return;
            await Linking.openURL(imageUrl);
          },
          icons: SYSTEM_ICONS.browser,
        },
      ],
      [imageUrl, saveImage],
    )
  );
};
