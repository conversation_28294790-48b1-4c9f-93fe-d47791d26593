import {LOGGER, onFirebaseRequest} from '../../firebase';
import {appUsersRef, streamAppUsers, updateAppUserById} from '../../firestore';
import {type HealthStats, StatusCodes, type UUIDString} from '../../types';
import {
  composeMiddleware,
  isDeepEqual,
  withApiKey,
  withValidPostRequest,
} from '../../utils';

/**
 * This endpoint can be used to transform the deprecated `healthDayStats` field on all appUser
 * documents to an empty array. This is necessary because the health data is stored in the `healthData`
 * subcollection of the appUser moving forward, and the `healthDayStats` field is no longer used.
 *
 * This can be run multiple times safely.
 */
export const transformHealthDayStats = onFirebaseRequest(
  {memory: '512MiB'},
  composeMiddleware(
    'transformHealthDayStats',
    async (_, response) => {
      const modifiedUserIds: UUIDString[] = [];
      const skippedUserIds: UUIDString[] = [];

      await streamAppUsers(async appUser => {
        try {
          // Get the document reference for this appUser
          const appUserDocRef = appUsersRef.doc(appUser.id);

          // Check if the user has a healthData subcollection
          const subcollections = await appUserDocRef.listCollections();
          const hasHealthDataSubcollection = subcollections.some(
            subcollection => subcollection.id === 'healthData',
          );

          // Skip if no healthData subcollection exists
          if (!hasHealthDataSubcollection) {
            skippedUserIds.push(appUser.id);
            return;
          }

          // Check if healthDayStats is already an empty array
          const currentHealthDayStats = appUser.healthDayStats;
          const targetHealthDayStats: HealthStats[] = [];

          // Skip if already has empty array (no change needed)
          if (isDeepEqual(currentHealthDayStats, targetHealthDayStats)) return;

          // Log the user being modified
          LOGGER.debug(`Setting healthDayStats to empty array for user: ${appUser.id}`);

          // Update the user document
          await updateAppUserById(appUser.id, {healthDayStats: targetHealthDayStats});

          // Track the modified user IDs
          modifiedUserIds.push(appUser.id);
        } catch (error) {
          LOGGER.error(`Error processing user ${appUser.id}:`, error);
        }
      });

      // Log user ids
      LOGGER.debug('Modified users', modifiedUserIds);
      LOGGER.debug('Skipped users', skippedUserIds);

      const successMessage = `Successfully transformed '${modifiedUserIds.length}' app users to have empty healthDayStats array, and skipped '${skippedUserIds.length}' users`;
      LOGGER.debug(successMessage);

      response
        .status(StatusCodes.OK_200)
        .send(successMessage)
        .end();
    },
    withValidPostRequest,
    withApiKey('************************************'),
  ),
);
