import type {HealthStats, WeightSample} from './appUserModel';
import type {IsoDate, IsoMonth, UUIDString} from './shared';
import type {TimeZones} from '../dates';

export type HealthData = {
  id: IsoMonth;
  // startedDateTime: RawTimestamp;
  // endedDateTime: RawTimestamp;
  healthStats: HealthStats[];
  weightSamples?: WeightSample[];
};

export type HealthDataMutationRequest = {
  operation: 'deleteAll' | 'deleteRange';
  userId: UUIDString;
  startDate?: IsoDate;
  endDate?: IsoDate;
  timeZone?: TimeZones;
};
